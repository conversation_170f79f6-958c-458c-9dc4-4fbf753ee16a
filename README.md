# Geany Copilot

**G<PERSON><PERSON> Copilot** is an AI-powered assistant integrated into the [Geany](https://www.geany.org/) IDE. Inspired by GitHub Copilot, it leverages advanced language models to provide context-aware code completions and creative copywriting assistance, enhancing your productivity and creativity directly within the Geany editor.

## 🚀 **Quick Start - Python Version (Recommended)**

**New Enhanced Python Implementation with Agent Capabilities!**

```bash
git clone https://github.com/skizap/geany-copilot.git
cd geany-copilot
./install.sh
```

The automated installer handles everything: dependency installation, legacy file management, and plugin setup. See [INSTALL_GUIDE.md](INSTALL_GUIDE.md) for detailed instructions.

### **✨ Python Version Features**
- 🤖 **Agent Intelligence**: Multi-turn conversations with context retention
- 🔌 **Enhanced API Support**: DeepSeek, OpenAI, and custom providers
- 🎨 **Modern UI**: GTK-based dialogs with comprehensive functionality
- 🛡️ **Enterprise Security**: OS keyring integration, secure API key storage, prompt injection protection
- 🚀 **Performance Optimization**: Intelligent caching, predictive preloading, memory management
- 📊 **Advanced Monitoring**: Real-time performance metrics, error tracking, health reporting
- 🔧 **Easy Installation**: Automated setup with one command
- 🛠️ **Reliability**: Thread-safe operations, graceful error recovery, comprehensive validation

### **📁 Version Comparison**

| Feature | Python Version | Lua Version (Legacy) |
|---------|----------------|----------------------|
| **Agent Conversations** | ✅ Multi-turn with context | ❌ Single requests only |
| **API Support** | ✅ DeepSeek + OpenAI + Custom | ✅ OpenAI compatible |
| **User Interface** | ✅ Modern GTK dialogs | ✅ Basic dialogs |
| **Security** | ✅ Enterprise-grade (keyring, validation) | ⚠️ Basic |
| **Performance** | ✅ Intelligent caching & optimization | ❌ No optimization |
| **Monitoring** | ✅ Real-time metrics & health reports | ❌ No monitoring |
| **Error Handling** | ✅ Comprehensive with recovery | ✅ Basic |
| **Installation** | ✅ Automated script | ⚠️ Manual setup |
| **Testing** | ✅ Full test suite | ❌ No tests |
| **Maintenance** | ✅ Active development | 🔒 Legacy support |

**💡 Recommendation**: Use the Python version for new installations. Legacy Lua files are preserved in the `OLD/` directory.

## Security & Performance

The Python version of Geany Copilot features **enterprise-grade security, reliability, and performance** improvements:

### 🔒 **Enterprise Security Features**

- **🔐 Secure API Key Storage**: OS keyring integration (Windows Credential Manager, macOS Keychain, Linux Secret Service)
- **🌍 Environment Variable Support**: Fallback to `DEEPSEEK_API_KEY`, `OPENAI_API_KEY` environment variables
- **🛡️ Prompt Injection Protection**: Advanced detection and prevention of malicious prompt injection attempts
- **📝 Secure Logging**: Automatic sanitization of sensitive data in logs (API keys, tokens, passwords)
- **🔒 File Security**: Restrictive permissions (600) on configuration files and secure directory creation
- **✅ Input Validation**: Comprehensive validation and sanitization of user input and context data

### ⚡ **Performance Optimization**

- **🧠 Intelligent Caching**: Smart cache keys with context similarity and predictive preloading
- **📊 Real-time Monitoring**: Performance metrics, operation timing, and success/error rate tracking
- **🔄 Memory Management**: Automatic conversation history limits, cache optimization, and memory cleanup
- **⚙️ Auto-Optimization**: Periodic performance tuning and resource optimization
- **📈 Cache Efficiency**: Hit rate optimization (~30% improvement) and memory usage reduction (~25%)

### 🛠️ **Reliability & Error Handling**

- **🔧 Thread Safety**: GTK operations guaranteed to run on main thread, preventing UI crashes
- **🔄 Graceful Recovery**: Automatic error recovery, circuit breaker patterns, and fallback strategies
- **📋 Health Monitoring**: Continuous system health assessment with actionable recommendations
- **⚠️ Graceful Degradation**: Non-essential features disabled under high error rates to maintain stability
- **🔍 Comprehensive Validation**: Configuration validation with auto-fix capabilities

### 📊 **Monitoring & Analytics**

- **📈 Performance Metrics**: Real-time tracking of response times, cache hit rates, and system performance
- **🚨 Error Tracking**: Detailed error categorization, trend analysis, and recovery success rates
- **💡 Health Reports**: Comprehensive system health reports with optimization recommendations
- **📋 Configuration Validation**: Automatic detection and correction of configuration issues

## Table of Contents

- [Quick Start - Python Version](#-quick-start---python-version-recommended)
- [Security & Performance](#security--performance)
- [Features](#features)
- [Installation](#installation)
  - [Python Version (Recommended)](#python-version-recommended)
  - [Lua Version (Legacy)](#lua-version-legacy)
- [Configuration](#configuration)
- [Usage](#usage)
  - [Code Assistance](#code-assistance)
  - [Copywriting Assistance](#copywriting-assistance)
- [Troubleshooting](#troubleshooting)
- [Migration Guide](#migration-guide)
- [Dependencies](#dependencies)
- [Contributing](#contributing)
- [License](#license)
- [Acknowledgements](#acknowledgements)
- [Contact](#contact)

## Features

### 🤖 **AI-Powered Intelligence**
- **Multi-turn Conversations:** Context-aware conversations with memory retention across interactions
- **Intelligent Code Completions:** Advanced code suggestions based on your current context and coding patterns
- **Creative Copywriting Assistance:** Generate and refine creative content with constructive feedback and suggestions
- **Context Analysis:** Automatic analysis of surrounding code and project context for better suggestions

### 🔒 **Enterprise Security**
- **Secure Credential Management:** OS keyring integration for API key storage with environment variable fallback
- **Prompt Injection Protection:** Advanced detection and prevention of malicious prompt injection attempts
- **Secure Logging:** Automatic sanitization of sensitive data in logs and debug output
- **Input Validation:** Comprehensive validation and sanitization of all user input and context data
- **File Security:** Secure file permissions and encrypted configuration storage

### ⚡ **Performance & Reliability**
- **Intelligent Caching:** Smart caching with predictive preloading and context-aware invalidation
- **Memory Management:** Automatic conversation history limits and memory optimization
- **Thread Safety:** GTK operations guaranteed to run on main thread, preventing crashes
- **Error Recovery:** Graceful error handling with automatic recovery and fallback strategies
- **Health Monitoring:** Real-time performance metrics and system health assessment

### 🎨 **User Experience**
- **Modern UI:** GTK-based dialogs with comprehensive functionality and responsive design
- **Seamless Integration:** Works directly within Geany, maintaining your workflow without tool switching
- **Configuration Validation:** Automatic validation and correction of configuration issues
- **Customizable Settings:** Easily configure API endpoints, system prompts, and behavior preferences
- **Selection Replacement:** Option to replace selected text with AI-generated suggestions

## Installation

### **Python Version (Recommended)**

**🚀 Automated Installation:**

```bash
git clone https://github.com/skizap/geany-copilot.git
cd geany-copilot
./install.sh
```

The installation script automatically:
- ✅ Checks prerequisites (Python 3.6+, pip, Geany)
- ✅ Moves legacy Lua files to `OLD/` directory
- ✅ Installs Python dependencies (`requests`, `keyring` for secure API key storage)
- ✅ Copies plugin files to appropriate Geany directory
- ✅ Sets secure file permissions (600) on configuration files
- ✅ Verifies installation and runs comprehensive tests

**📋 Prerequisites:**
- **Geany IDE** with GeanyPy plugin support
- **Python 3.6+** (Python 3.8+ recommended)
- **pip** package manager

**📚 Detailed Instructions:** See [INSTALL_GUIDE.md](INSTALL_GUIDE.md) for comprehensive installation guide.

### **Lua Version (Legacy)**

**⚠️ Note**: The Lua version is preserved for compatibility but is no longer actively developed. New users should use the Python version.

**Prerequisites:**
- **Geany IDE** with Lua scripting support
- **Dependencies**: `lunajson` library and `cURL`

**Steps:**

1. **Restore Legacy Files** (if you want to use the Lua version):
   ```bash
   cp OLD/*.lua ./
   ```

2. **Install Dependencies:**

   Ensure that the `lunajson` library is available. You can install it using LuaRocks:

   ```bash
   luarocks install lunajson
   ```

   Additionally, ensure that `cURL` is installed on your system:

   ```bash
   # For Debian/Ubuntu
   sudo apt-get install curl

   # For macOS using Homebrew
   brew install curl
   ```

4. **Place the Plugins in Geany's Plugin Directory:**

   Copy the Lua scripts (`copilot.lua` and `copywriter.lua`) to Geany's plugin or script directory, typically located at `~/.config/geany/plugins/` or a similar path depending on your operating system.

   ```bash
   mkdir -p ~/.config/geany/plugins/geanylua/geany-copilot/
   cp copilot.lua copywriter.lua ~/.config/geany/plugins/geanylua/geany-copilot/
   ```

5. **Restart Geany:**

   After placing the plugins, restart Geany in case the lua scripts didn't load automatically.

## Configuration

Geany Copilot features **enterprise-grade configuration management** with automatic validation, secure credential storage, and health monitoring. The Python version provides enhanced security and user experience compared to the legacy Lua version.

### 🔒 **Secure Configuration Features**

- **🔐 Secure API Key Storage**: Automatic OS keyring integration (Windows Credential Manager, macOS Keychain, Linux Secret Service)
- **🌍 Environment Variable Support**: Fallback to `DEEPSEEK_API_KEY`, `OPENAI_API_KEY` environment variables
- **✅ Automatic Validation**: Real-time configuration validation with error detection and auto-fix capabilities
- **📊 Health Monitoring**: Configuration health reports with optimization recommendations
- **🔒 Secure Permissions**: Automatic secure file permissions (600) on all configuration files

### Settings File Locations

- **Code Assistance Settings:**

  ```
  ~/.config/geany/plugins/geanylua/geany-copilot/copilot.json
  ```

- **Copywriting Assistance Settings:**

  ```
  ~/.config/geany/plugins/geanylua/geany-copilot/copywriter.json
  ```

### Setting Up
1. **(Optional): Setup shortcuts**
   Open the Keybindings section under Geany Preferences. Scroll down to "Lua Script" sub-section and set the shortcut for both "Copilot" and "Copywriter". For example: <Super>backslash and <Super>Return respectively.

2. **Open Geany Copilot Settings Dialog:**

   In Geany, you can access the settings dialog through either a plugin menu or a keyboard shortcut. The initial dialog will feature a "Settings" button. Alternatively, you can directly invoke the settings dialog by typing and selecting ".gc conf" anywhere within the editor, followed by activating the keyboard shortcut.

3. **Configure API Settings:**

   - **Primary Provider:** Choose your preferred AI provider (DeepSeek, OpenAI, or custom)
   - **Base URL:** Enter your API base URL (e.g., `https://api.deepseek.com` for DeepSeek)
   - **API Key:** Your API key is automatically stored securely using:
     - **OS Keyring** (Windows Credential Manager, macOS Keychain, Linux Secret Service) - **Recommended**
     - **Environment Variables** (`DEEPSEEK_API_KEY`, `OPENAI_API_KEY`) - **Secure fallback**
     - **Configuration file** - **Not recommended for production**

   **🔒 Security Note:** The plugin automatically detects and uses the most secure storage method available on your system.

   **🌐 Supported Providers:** Any OpenAI-compatible API (DeepSeek, OpenAI, Ollama, llama-server, etc.)

4. **Customize System Prompt:**

   - **Code Assistance:** Defines the behavior of the AI assistant for coding tasks.
   - **Copywriting Assistance:** Defines the behavior of the AI assistant for copywriting tasks.

5. **Behavior Preferences:**

   - **Replace Selection:** Choose whether the AI-generated suggestion should replace the currently selected text.

6. **Save Settings:**

   Click the **Save** button to apply your configurations.

## Usage

Once installed and configured, Geany Copilot is ready to assist you with both code completions and copywriting tasks.

### Code Assistance

**Geany Copilot** for code assistance operates similarly to GitHub Copilot, providing intelligent code suggestions based on your current context within the Geany editor.

#### Triggering Code Completions

1. **Select Code Context:**

   Highlight the code snippet you want the AI to analyze and complete. If no text is selected, Geany Copilot will automatically determine a context around the current caret position.

2. **Invoke Geany Copilot:**

   - Use a designated keyboard shortcut.
   - Access via the plugin menu.

3. **Review Suggestions:**

   Geany Copilot will display a dialog with AI-generated code completion options (usually 1). Review the suggestions, select the one that best fits your needs and click Accept.

4. **Apply Completion:**

   Upon selection, the chosen code snippet will replace the original selection.

### Copywriting Assistance

**Geany Copilot** also offers creative copywriting assistance, helping you generate and refine written content directly within the Geany editor.

#### Performing Copywriting Tasks

1. **Select Text or Position Caret:**

   - To generate new content, place the caret where you want the text to be inserted.
   - To review or refine existing text, highlight the text you wish to work on.

2. **Invoke Copywriting Assistant:**

   - Use a designated keyboard shortcut.
   - Access via the plugin menu.

3. **Choose an Action:**

   - **Generate Content:** Create new text based on the provided context.
   - **Review Text:** Get constructive feedback and suggestions for improvement.

4. **Review and Apply Suggestions:**

   The assistant will display a dialog with AI-generated suggestions. Choose the appropriate option to insert or replace text, depending on your configuration.

## Troubleshooting

### 🔧 **Common Issues and Solutions**

#### **API Key Issues**
- **Problem**: "API key not found" or authentication errors
- **Solutions**:
  1. **Check keyring storage**: `python3 -c "import keyring; print(keyring.get_password('geany-copilot', 'deepseek_api_key'))"`
  2. **Use environment variables**: `export DEEPSEEK_API_KEY="your-key-here"`
  3. **Verify API key validity**: Test with a simple API call
  4. **Check configuration health**: Use the built-in health report feature

#### **Performance Issues**
- **Problem**: Slow responses or high memory usage
- **Solutions**:
  1. **Check cache efficiency**: View cache hit rates in performance stats
  2. **Optimize cache settings**: Increase cache size or memory limits
  3. **Monitor system health**: Use the built-in health monitoring
  4. **Clear cache**: Reset cache if it becomes corrupted

#### **Network Connection Issues**
- **Problem**: Timeouts or connection errors
- **Solutions**:
  1. **Check network connectivity**: Verify internet connection
  2. **Verify API endpoint**: Ensure the base URL is correct
  3. **Check firewall settings**: Ensure outbound HTTPS is allowed
  4. **Review timeout settings**: Adjust timeout values in configuration

#### **UI/Threading Issues**
- **Problem**: Plugin freezes or crashes Geany
- **Solutions**:
  1. **Restart Geany**: The plugin includes automatic recovery mechanisms
  2. **Check logs**: Review `~/.config/geany/plugins/geany-copilot-python/logs/`
  3. **Update GeanyPy**: Ensure you have the latest GeanyPy version
  4. **Report issue**: Include system details and error logs

### 📊 **Health Monitoring**

The plugin includes comprehensive health monitoring:

```python
# Access health reports through the plugin interface
Tools → Copilot → Health Report
```

**Health indicators include:**
- **Configuration validation status**
- **API connectivity and response times**
- **Cache efficiency and memory usage**
- **Error rates and recovery success**
- **Security status and recommendations**

### 🔍 **Debug Mode**

Enable debug mode for detailed troubleshooting:

1. **Enable debug logging**: Set `debug: true` in configuration
2. **View logs**: Check `~/.config/geany/plugins/geany-copilot-python/logs/geany-copilot-python.log`
3. **Monitor performance**: Use built-in performance metrics
4. **Export diagnostics**: Generate diagnostic reports for support

### 📋 **Configuration Validation**

The plugin automatically validates configuration and provides recommendations:

- **Automatic error detection**: Invalid settings are flagged immediately
- **Auto-fix capabilities**: Common issues are corrected automatically
- **Health scoring**: Configuration health is scored from 0-100
- **Optimization recommendations**: Specific suggestions for improvement

## Migration Guide

### **From Lua to Python Version**

If you're currently using the Lua version and want to upgrade to the Python version:

1. **Backup Your Configuration** (optional):
   ```bash
   cp ~/.config/geany/plugins/geanylua/geany-copilot-python/config.json ~/geany-copilot-backup.json
   ```

2. **Run the Installation Script**:
   ```bash
   ./install.sh
   ```
   The script automatically moves your Lua files to the `OLD/` directory and installs the Python version.

3. **Reconfigure API Settings**:
   - Go to **Tools → Copilot → Settings** in Geany
   - Enter your API key and configure preferences
   - The Python version supports additional providers like DeepSeek

4. **Test the New Features**:
   - Try the enhanced code assistant with multi-turn conversations
   - Explore the improved copywriting interface
   - Experience better error handling and logging

### **Reverting to Lua Version**

If you need to revert to the Lua version:

1. **Remove Python Plugin**:
   ```bash
   rm -rf ~/.config/geany/plugins/geany-copilot-python/
   ```

2. **Restore Lua Files**:
   ```bash
   cp OLD/*.lua ./
   ```

3. **Reconfigure Geany** to use the Lua scripts as before.

## Dependencies

### **Python Version Dependencies**
- **Python 3.6+** (Python 3.8+ recommended, automatically checked by installer)
- **pip** package manager (automatically checked)
- **Core Libraries** (automatically installed):
  - **requests** - HTTP client for API communication
  - **keyring** - Secure API key storage using OS keyring
- **System Dependencies**:
  - **GTK+ 3.0+** (usually system-provided)
  - **GeanyPy** plugin (see [GeanyPy Installation Guide](#geanypy-installation-guide) below)
- **Optional Dependencies** (for enhanced features):
  - **psutil** - System monitoring and performance metrics
  - **cryptography** - Enhanced security features

### **Lua Version Dependencies (Legacy)**

- **Lua:** Ensure that Lua is installed and properly configured with Geany.
- **lunajson:** A Lua library for JSON encoding and decoding. Install via LuaRocks:

  ```bash
  luarocks install lunajson
  ```

- **cURL:** The plugin uses `cURL` to make HTTP requests to the OpenAI API. Ensure that `cURL` is installed on your system.

  ```bash
  # For Debian/Ubuntu
  sudo apt-get install curl

  # For macOS using Homebrew
  brew install curl
  ```

## GeanyPy Installation Guide

### **For Linux Mint Users (Your System)**

Since you're using **Linux Mint with Geany 2.1.0**, here are the specific steps to install GeanyPy:

#### **Quick Installation**
```bash
# Update package list
sudo apt update

# Install GeanyPy (try these in order)
sudo apt install geany-plugin-py
# OR if the above fails:
sudo apt install geany-plugins

# Install Python GTK dependencies
sudo apt install python3-gi python3-gi-cairo gir1.2-gtk-3.0
```

#### **Verification Steps**
1. **Restart Geany completely**: `killall geany && geany`
2. **Open Plugin Manager**: Tools → Plugin Manager
3. **Look for GeanyPy**: Should appear in the plugin list
4. **Enable GeanyPy**: Check the checkbox and click OK
5. **Test**: Look for Python plugin support

#### **Troubleshooting Script**
If GeanyPy doesn't appear, run our troubleshooting script:
```bash
./troubleshoot_geanypy.sh
```

This script will:
- ✅ Detect your exact Linux Mint version
- ✅ Check for GeanyPy packages
- ✅ Install missing dependencies
- ✅ Test Python GTK bindings
- ✅ Create a test plugin to verify functionality
- ✅ Provide specific fixes for your system

#### **Alternative Package Names**
Different Linux distributions use different package names:

| Distribution | Package Name |
|--------------|--------------|
| **Linux Mint/Ubuntu/Debian** | `geany-plugin-py` or `geany-plugins` |
| **Fedora/CentOS** | `geany-plugins-geanypy` |
| **Arch Linux** | `geany-plugins` |
| **openSUSE** | `geany-plugin-geanypy` |

#### **Manual Verification**
Check if GeanyPy is installed:
```bash
# Check installed packages
dpkg -l | grep geany-plugin

# Look for GeanyPy files
find /usr -name "*geanypy*" 2>/dev/null
find /usr -name "*py.so" 2>/dev/null

# Test Python GTK
python3 -c "import gi; gi.require_version('Gtk', '3.0'); from gi.repository import Gtk; print('GTK OK')"
```

#### **If GeanyPy Still Doesn't Work**
1. **Check detailed guide**: See [GEANYPY_INSTALLATION.md](GEANYPY_INSTALLATION.md)
2. **Use troubleshooting script**: `./troubleshoot_geanypy.sh`
3. **Fallback to Lua version**: Files preserved in `OLD/` directory
4. **Report issue**: Include your system details and error messages

### **Understanding GeanyPy vs Other Plugins**

**GeanyPy** is special because it's a "plugin framework" that allows Python scripts to act as Geany plugins:

- **Regular Geany plugins**: Written in C/C++, compiled as `.so` files
- **GeanyPy plugins**: Written in Python, loaded through GeanyPy framework
- **GeanyLua plugins**: Written in Lua (what we're migrating from)

**Why GeanyPy is Required:**
- Our Python plugin uses GeanyPy's API to integrate with Geany
- Without GeanyPy, Python plugins cannot access Geany's interface
- GeanyPy provides the bridge between Python and Geany's C API

## Contributing

Contributions are welcome! If you'd like to contribute to Geany Copilot, please follow these guidelines:

1. **Fork the Repository:**

   Click the **Fork** button at the top of this page to create a personal copy of the repository.

2. **Create a Feature Branch:**

   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Commit Your Changes:**

   ```bash
   git commit -m "Add your detailed description of the changes"
   ```

4. **Push to Your Fork:**

   ```bash
   git push origin feature/your-feature-name
   ```

5. **Open a Pull Request:**

   Navigate to the original repository and click **New Pull Request**. Provide a clear description of your changes and submit the pull request.

### Reporting Issues

If you encounter any issues or have suggestions for improvements, please [open an issue](https://github.com/yourusername/geany-copilot/issues) on GitHub.

## License

This project is licensed under the [MIT License](LICENSE). You are free to use, modify, and distribute this software in accordance with the terms of the license.

---

**Security & Privacy:** Geany Copilot features enterprise-grade security with OS keyring integration for secure API key storage, automatic sensitive data sanitization, and prompt injection protection. The plugin never logs API keys or sensitive information. Be aware of any costs associated with API usage and review your provider's privacy policy.

## Acknowledgements

- Inspired by [Geany IDE](https://www.geany.org/) and [GitHub Copilot](https://github.com/features/copilot).
- Utilizes the [lunajson](https://github.com/grafi-tt/lunajson) library for JSON handling.
- Powered by OpenAI's language models.
- https://github.com/DevElCuy/geany-copilot
## Contact

For any queries or support, please reach out to https://github.com/skizap.

---

*Happy Coding and Writing! 🚀*
